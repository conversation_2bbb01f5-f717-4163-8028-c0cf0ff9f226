<mxfile host="app.diagrams.net" modified="2025-01-12T00:00:00.000Z" agent="5.0" etag="updated" version="22.1.16" type="device">
  <diagram name="图注意力特征学习" id="graph-attention-learning">
    <mxGraphModel dx="1800" dy="1000" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="800" math="1" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- 输入特征模块 - 改进配色和布局 -->
        <mxCell id="input_module" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;strokeWidth=2;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="60" y="160" width="200" height="120" as="geometry" />
        </mxCell>

        <mxCell id="input_title" value="输入特征层" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#1976D2;" vertex="1" parent="1">
          <mxGeometry x="110" y="130" width="100" height="25" as="geometry" />
        </mxCell>

        <!-- 节点特征矩阵 -->
        <mxCell id="feature_matrix" value="节点特征&#xa;H ∈ ℝⁿˣᵈ" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#1976D2;fontSize=11;fontColor=#1976D2;align=center;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="180" width="70" height="40" as="geometry" />
        </mxCell>

        <!-- 边特征矩阵 -->
        <mxCell id="edge_features" value="边特征&#xa;E ∈ ℝᵐˣᵈᵉ" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#1976D2;fontSize=11;fontColor=#1976D2;align=center;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="170" y="180" width="70" height="40" as="geometry" />
        </mxCell>

        <!-- RSSI信号说明 -->
        <mxCell id="rssi_label" value="RSSI信号强度特征" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="80" y="240" width="160" height="20" as="geometry" />
        </mxCell>

        <!-- 箭头1 - 改进样式 -->
        <mxCell id="arrow1" value="" style="endArrow=classic;html=1;strokeColor=#1976D2;strokeWidth=3;fillColor=#1976D2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="280" y="220" as="sourcePoint" />
            <mxPoint x="340" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- GATv2注意力机制模块 - 改进设计 -->
        <mxCell id="attention_module" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF3E0;strokeColor=#F57C00;strokeWidth=2;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="360" y="140" width="320" height="180" as="geometry" />
        </mxCell>

        <mxCell id="attention_title" value="GATv2注意力计算层" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#F57C00;" vertex="1" parent="1">
          <mxGeometry x="470" y="110" width="140" height="25" as="geometry" />
        </mxCell>

        <!-- 注意力计算流程 -->
        <mxCell id="attention_flow" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#F57C00;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="380" y="160" width="280" height="140" as="geometry" />
        </mxCell>

        <!-- 步骤1：特征拼接 -->
        <mxCell id="step1_box" value="特征拼接" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF8E1;strokeColor=#F57C00;strokeWidth=1;fontSize=12;fontStyle=1;fontColor=#F57C00;" vertex="1" parent="1">
          <mxGeometry x="390" y="170" width="80" height="30" as="geometry" />
        </mxCell>

        <mxCell id="step1_formula" value="[hᵢ ∥ hⱼ ∥ eᵢⱼ]" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=2;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="480" y="175" width="100" height="20" as="geometry" />
        </mxCell>

        <!-- 步骤2：线性变换 -->
        <mxCell id="step2_box" value="线性变换" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF8E1;strokeColor=#F57C00;strokeWidth=1;fontSize=12;fontStyle=1;fontColor=#F57C00;" vertex="1" parent="1">
          <mxGeometry x="390" y="210" width="80" height="30" as="geometry" />
        </mxCell>

        <mxCell id="step2_formula" value="W · [hᵢ ∥ hⱼ ∥ eᵢⱼ]" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=2;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="480" y="215" width="120" height="20" as="geometry" />
        </mxCell>

        <!-- 步骤3：激活与归一化 -->
        <mxCell id="step3_box" value="注意力权重" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF8E1;strokeColor=#F57C00;strokeWidth=1;fontSize=12;fontStyle=1;fontColor=#F57C00;" vertex="1" parent="1">
          <mxGeometry x="390" y="250" width="80" height="30" as="geometry" />
        </mxCell>

        <mxCell id="step3_formula" value="αᵢⱼ = softmax(LeakyReLU(·))" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=2;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="480" y="255" width="160" height="20" as="geometry" />
        </mxCell>

        <!-- 连接箭头 -->
        <mxCell id="flow_arrow1" value="" style="endArrow=classic;html=1;strokeColor=#F57C00;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="430" y="200" as="sourcePoint" />
            <mxPoint x="430" y="210" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="flow_arrow2" value="" style="endArrow=classic;html=1;strokeColor=#F57C00;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="430" y="240" as="sourcePoint" />
            <mxPoint x="430" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 箭头2 - 改进样式 -->
        <mxCell id="arrow2" value="" style="endArrow=classic;html=1;strokeColor=#1976D2;strokeWidth=3;fillColor=#1976D2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="700" y="230" as="sourcePoint" />
            <mxPoint x="760" y="230" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 多头注意力聚合模块 - 改进设计 -->
        <mxCell id="multihead_module" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F3E5F5;strokeColor=#7B1FA2;strokeWidth=2;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="780" y="140" width="320" height="180" as="geometry" />
        </mxCell>

        <mxCell id="multihead_title" value="多头注意力聚合层" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#7B1FA2;" vertex="1" parent="1">
          <mxGeometry x="890" y="110" width="140" height="25" as="geometry" />
        </mxCell>

        <!-- 多头注意力可视化 -->
        <mxCell id="multihead_container" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#7B1FA2;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="800" y="160" width="280" height="140" as="geometry" />
        </mxCell>

        <!-- 多个注意力头 -->
        <mxCell id="head1" value="Head 1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1BEE7;strokeColor=#7B1FA2;fontSize=11;fontColor=#7B1FA2;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="820" y="180" width="60" height="30" as="geometry" />
        </mxCell>

        <mxCell id="head2" value="Head 2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1BEE7;strokeColor=#7B1FA2;fontSize=11;fontColor=#7B1FA2;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="890" y="180" width="60" height="30" as="geometry" />
        </mxCell>

        <mxCell id="head3" value="Head H" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1BEE7;strokeColor=#7B1FA2;fontSize=11;fontColor=#7B1FA2;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="960" y="180" width="60" height="30" as="geometry" />
        </mxCell>

        <mxCell id="dots" value="⋯" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontColor=#7B1FA2;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1030" y="190" width="20" height="15" as="geometry" />
        </mxCell>

        <!-- 拼接操作 -->
        <mxCell id="concat_operation" value="特征拼接与融合" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F8BBD9;strokeColor=#7B1FA2;strokeWidth=1;fontSize=12;fontStyle=1;fontColor=#7B1FA2;" vertex="1" parent="1">
          <mxGeometry x="820" y="230" width="200" height="30" as="geometry" />
        </mxCell>

        <mxCell id="concat_formula" value="H' = Concat(H₁, H₂, ..., Hₕ) + Residual" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=2;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="820" y="270" width="200" height="20" as="geometry" />
        </mxCell>

        <!-- 多头连接箭头 -->
        <mxCell id="multihead_arrow1" value="" style="endArrow=classic;html=1;strokeColor=#7B1FA2;strokeWidth=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="850" y="210" as="sourcePoint" />
            <mxPoint x="850" y="230" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="multihead_arrow2" value="" style="endArrow=classic;html=1;strokeColor=#7B1FA2;strokeWidth=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="920" y="210" as="sourcePoint" />
            <mxPoint x="920" y="230" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="multihead_arrow3" value="" style="endArrow=classic;html=1;strokeColor=#7B1FA2;strokeWidth=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="990" y="210" as="sourcePoint" />
            <mxPoint x="990" y="230" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 箭头3 - 改进样式 -->
        <mxCell id="arrow3" value="" style="endArrow=classic;html=1;strokeColor=#1976D2;strokeWidth=3;fillColor=#1976D2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1120" y="230" as="sourcePoint" />
            <mxPoint x="1180" y="230" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 输出模块 - 改进设计 -->
        <mxCell id="output_module" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F5E8;strokeColor=#4CAF50;strokeWidth=2;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="1200" y="160" width="200" height="120" as="geometry" />
        </mxCell>

        <mxCell id="output_title" value="输出特征层" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#4CAF50;" vertex="1" parent="1">
          <mxGeometry x="1250" y="130" width="100" height="25" as="geometry" />
        </mxCell>

        <!-- 更新后的特征 -->
        <mxCell id="updated_features" value="更新特征&#xa;H' ∈ ℝⁿˣᵈ'" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#4CAF50;fontSize=11;fontColor=#4CAF50;align=center;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1220" y="180" width="70" height="40" as="geometry" />
        </mxCell>

        <!-- 激活函数 -->
        <mxCell id="activation_function" value="ELU激活" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#4CAF50;fontSize=11;fontColor=#4CAF50;align=center;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1310" y="180" width="70" height="40" as="geometry" />
        </mxCell>

        <!-- 输出说明 -->
        <mxCell id="output_label" value="增强的节点表示" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="1220" y="240" width="160" height="20" as="geometry" />
        </mxCell>

        <!-- 异构图示例 - 改进设计 -->
        <mxCell id="graph_example" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FAFAFA;strokeColor=#757575;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="60" y="360" width="380" height="140" as="geometry" />
        </mxCell>

        <mxCell id="graph_title" value="RFID异构图结构" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#424242;" vertex="1" parent="1">
          <mxGeometry x="200" y="330" width="140" height="25" as="geometry" />
        </mxCell>

        <!-- 天线节点层 -->
        <mxCell id="antenna_layer_label" value="天线节点" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#1976D2;" vertex="1" parent="1">
          <mxGeometry x="80" y="380" width="60" height="20" as="geometry" />
        </mxCell>

        <mxCell id="antenna_node1" value="A₁" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;fontSize=11;fontColor=#1976D2;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="160" y="375" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="antenna_node2" value="A₂" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;fontSize=11;fontColor=#1976D2;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="210" y="375" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="antenna_node3" value="A₃" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;fontSize=11;fontColor=#1976D2;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="260" y="375" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="antenna_node4" value="A₄" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;fontSize=11;fontColor=#1976D2;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="310" y="375" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="antenna_node5" value="A₅" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;fontSize=11;fontColor=#1976D2;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="360" y="375" width="30" height="30" as="geometry" />
        </mxCell>

        <!-- 标签节点层 -->
        <mxCell id="tag_layer_label" value="标签节点" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#E91E63;" vertex="1" parent="1">
          <mxGeometry x="80" y="430" width="60" height="20" as="geometry" />
        </mxCell>

        <mxCell id="tag_node1" value="T₁" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FCE4EC;strokeColor=#E91E63;fontSize=11;fontColor=#E91E63;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="160" y="425" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="tag_node2" value="T₂" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FCE4EC;strokeColor=#E91E63;fontSize=11;fontColor=#E91E63;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="210" y="425" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="tag_node3" value="T₃" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FCE4EC;strokeColor=#E91E63;fontSize=11;fontColor=#E91E63;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="260" y="425" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="tag_node4" value="T₄" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FCE4EC;strokeColor=#E91E63;fontSize=11;fontColor=#E91E63;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="310" y="425" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="tag_node5" value="T₅" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FCE4EC;strokeColor=#E91E63;fontSize=11;fontColor=#E91E63;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="360" y="425" width="30" height="30" as="geometry" />
        </mxCell>

        <!-- 连接边 - 改进设计 -->
        <!-- 天线到标签的连接 -->
        <mxCell id="edge1" value="" style="endArrow=none;html=1;strokeColor=#FF9800;strokeWidth=2;opacity=80;" edge="1" parent="1" source="antenna_node1" target="tag_node1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge2" value="" style="endArrow=none;html=1;strokeColor=#FF9800;strokeWidth=2;opacity=80;" edge="1" parent="1" source="antenna_node1" target="tag_node2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge3" value="" style="endArrow=none;html=1;strokeColor=#FF9800;strokeWidth=2;opacity=80;" edge="1" parent="1" source="antenna_node2" target="tag_node2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge4" value="" style="endArrow=none;html=1;strokeColor=#FF9800;strokeWidth=2;opacity=80;" edge="1" parent="1" source="antenna_node2" target="tag_node3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge5" value="" style="endArrow=none;html=1;strokeColor=#FF9800;strokeWidth=2;opacity=80;" edge="1" parent="1" source="antenna_node3" target="tag_node3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge6" value="" style="endArrow=none;html=1;strokeColor=#FF9800;strokeWidth=2;opacity=80;" edge="1" parent="1" source="antenna_node3" target="tag_node4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge7" value="" style="endArrow=none;html=1;strokeColor=#FF9800;strokeWidth=2;opacity=80;" edge="1" parent="1" source="antenna_node4" target="tag_node4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge8" value="" style="endArrow=none;html=1;strokeColor=#FF9800;strokeWidth=2;opacity=80;" edge="1" parent="1" source="antenna_node4" target="tag_node5">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge9" value="" style="endArrow=none;html=1;strokeColor=#FF9800;strokeWidth=2;opacity=80;" edge="1" parent="1" source="antenna_node5" target="tag_node5">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 图例 - 改进设计 -->
        <mxCell id="legend_box" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#757575;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="480" y="360" width="220" height="140" as="geometry" />
        </mxCell>

        <mxCell id="legend_title" value="图例说明" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#424242;" vertex="1" parent="1">
          <mxGeometry x="560" y="330" width="80" height="25" as="geometry" />
        </mxCell>

        <!-- 图例项目 - 改进样式 -->
        <mxCell id="legend_antenna" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;" vertex="1" parent="1">
          <mxGeometry x="500" y="380" width="25" height="25" as="geometry" />
        </mxCell>
        <mxCell id="legend_antenna_text" value="天线节点 (RFID读写器)" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#424242;" vertex="1" parent="1">
          <mxGeometry x="535" y="385" width="140" height="15" as="geometry" />
        </mxCell>

        <mxCell id="legend_tag" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FCE4EC;strokeColor=#E91E63;" vertex="1" parent="1">
          <mxGeometry x="500" y="415" width="25" height="25" as="geometry" />
        </mxCell>
        <mxCell id="legend_tag_text" value="标签节点 (RFID标签)" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#424242;" vertex="1" parent="1">
          <mxGeometry x="535" y="420" width="140" height="15" as="geometry" />
        </mxCell>

        <mxCell id="legend_edge" value="" style="endArrow=none;html=1;strokeColor=#FF9800;strokeWidth=3;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="500" y="455" as="sourcePoint" />
            <mxPoint x="525" y="455" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="legend_edge_text" value="RSSI信号连接" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#424242;" vertex="1" parent="1">
          <mxGeometry x="535" y="450" width="100" height="15" as="geometry" />
        </mxCell>

        <!-- 主图标题 - 改进样式 -->
        <mxCell id="figure_caption" value="图 1. 基于GATv2的RFID异构图注意力网络架构" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#1976D2;" vertex="1" parent="1">
          <mxGeometry x="500" y="540" width="400" height="30" as="geometry" />
        </mxCell>

        <!-- 数学公式说明 -->
        <mxCell id="formula_explanation" value="其中：H为节点特征矩阵，E为边特征矩阵，α为注意力权重" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=2;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="500" y="570" width="400" height="20" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
