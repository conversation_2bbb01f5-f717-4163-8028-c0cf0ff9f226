<mxfile host="app.diagrams.net" modified="2025-01-12T00:00:00.000Z" agent="5.0" etag="simplified" version="22.1.16" type="device">
  <diagram name="图注意力特征学习" id="graph-attention-learning">
    <mxGraphModel dx="1400" dy="800" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="600" math="1" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- 输入层 -->
        <mxCell id="input_layer" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="50" y="150" width="200" height="120" as="geometry" />
        </mxCell>

        <mxCell id="input_title" value="异构图输入" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#1976D2;" vertex="1" parent="1">
          <mxGeometry x="120" y="120" width="100" height="25" as="geometry" />
        </mxCell>

        <!-- 节点特征 -->
        <mxCell id="node_features" value="节点特征&#xa;H ∈ ℝⁿˣᵈ" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#1976D2;fontSize=11;fontColor=#1976D2;align=center;" vertex="1" parent="1">
          <mxGeometry x="70" y="170" width="70" height="40" as="geometry" />
        </mxCell>

        <!-- 边特征 -->
        <mxCell id="edge_features" value="边特征&#xa;E ∈ ℝᵐˣᵈᵉ" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#1976D2;fontSize=11;fontColor=#1976D2;align=center;" vertex="1" parent="1">
          <mxGeometry x="160" y="170" width="70" height="40" as="geometry" />
        </mxCell>

        <!-- RSSI信号 -->
        <mxCell id="rssi_signal" value="RSSI信号强度" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="70" y="230" width="160" height="20" as="geometry" />
        </mxCell>

        <!-- 箭头1 -->
        <mxCell id="arrow1" value="" style="endArrow=classic;html=1;strokeColor=#1976D2;strokeWidth=3;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="270" y="210" as="sourcePoint" />
            <mxPoint x="320" y="210" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- GATv2注意力层 -->
        <mxCell id="attention_layer" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF3E0;strokeColor=#F57C00;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="340" y="150" width="300" height="120" as="geometry" />
        </mxCell>

        <mxCell id="attention_title" value="GATv2注意力机制" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#F57C00;" vertex="1" parent="1">
          <mxGeometry x="440" y="120" width="140" height="25" as="geometry" />
        </mxCell>

        <!-- 注意力计算 -->
        <mxCell id="attention_calc" value="注意力权重计算" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#F57C00;fontSize=12;fontColor=#F57C00;align=center;" vertex="1" parent="1">
          <mxGeometry x="360" y="170" width="120" height="30" as="geometry" />
        </mxCell>

        <!-- 特征聚合 -->
        <mxCell id="feature_agg" value="特征聚合" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#F57C00;fontSize=12;fontColor=#F57C00;align=center;" vertex="1" parent="1">
          <mxGeometry x="500" y="170" width="120" height="30" as="geometry" />
        </mxCell>

        <!-- 注意力公式 -->
        <mxCell id="attention_formula" value="α_ij = softmax(LeakyReLU(W[h_i∥h_j∥e_ij]))" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=2;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="360" y="220" width="260" height="20" as="geometry" />
        </mxCell>

        <!-- 箭头2 -->
        <mxCell id="arrow2" value="" style="endArrow=classic;html=1;strokeColor=#F57C00;strokeWidth=3;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="660" y="210" as="sourcePoint" />
            <mxPoint x="710" y="210" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 输出层 -->
        <mxCell id="output_layer" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F5E8;strokeColor=#4CAF50;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="730" y="150" width="200" height="120" as="geometry" />
        </mxCell>

        <mxCell id="output_title" value="输出层" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#4CAF50;" vertex="1" parent="1">
          <mxGeometry x="800" y="120" width="80" height="25" as="geometry" />
        </mxCell>

        <!-- 更新特征 -->
        <mxCell id="updated_features" value="更新特征&#xa;H' ∈ ℝⁿˣᵈ'" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#4CAF50;fontSize=11;fontColor=#4CAF50;align=center;" vertex="1" parent="1">
          <mxGeometry x="750" y="170" width="70" height="40" as="geometry" />
        </mxCell>

        <!-- 位置预测 -->
        <mxCell id="position_pred" value="位置预测&#xa;(x, y)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#4CAF50;fontSize=11;fontColor=#4CAF50;align=center;" vertex="1" parent="1">
          <mxGeometry x="840" y="170" width="70" height="40" as="geometry" />
        </mxCell>

        <!-- ELU激活 -->
        <mxCell id="elu_activation" value="ELU激活函数" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="750" y="230" width="160" height="20" as="geometry" />
        </mxCell>

        <!-- 残差连接 -->
        <mxCell id="residual_connection" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F5E8;strokeColor=#4CAF50;strokeWidth=2;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="940" y="80" width="200" height="280" as="geometry" />
        </mxCell>

        <mxCell id="residual_title" value="残差连接机制" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#4CAF50;" vertex="1" parent="1">
          <mxGeometry x="990" y="50" width="120" height="25" as="geometry" />
        </mxCell>

        <!-- 残差连接示意 -->
        <mxCell id="residual_input" value="输入特征&#xa;h⁽ˡ⁾" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#4CAF50;fontSize=11;fontColor=#4CAF50;align=center;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="960" y="100" width="70" height="40" as="geometry" />
        </mxCell>

        <mxCell id="residual_transform" value="特征变换&#xa;F(h⁽ˡ⁾)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#4CAF50;fontSize=11;fontColor=#4CAF50;align=center;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1050" y="100" width="70" height="40" as="geometry" />
        </mxCell>

        <mxCell id="residual_add" value="+" style="ellipse;whiteSpace=wrap;html=1;fillColor=#C8E6C9;strokeColor=#4CAF50;fontSize=16;fontColor=#4CAF50;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1010" y="180" width="30" height="30" as="geometry" />
        </mxCell>

        <mxCell id="residual_output" value="输出特征&#xa;h⁽ˡ⁺¹⁾ = h⁽ˡ⁾ + F(h⁽ˡ⁾)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#4CAF50;fontSize=11;fontColor=#4CAF50;align=center;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="960" y="240" width="160" height="40" as="geometry" />
        </mxCell>

        <!-- 残差连接箭头 -->
        <mxCell id="residual_arrow1" value="" style="endArrow=classic;html=1;strokeColor=#4CAF50;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="995" y="140" as="sourcePoint" />
            <mxPoint x="1010" y="180" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="residual_arrow2" value="" style="endArrow=classic;html=1;strokeColor=#4CAF50;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1085" y="140" as="sourcePoint" />
            <mxPoint x="1030" y="180" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="residual_arrow3" value="" style="endArrow=classic;html=1;strokeColor=#4CAF50;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1025" y="210" as="sourcePoint" />
            <mxPoint x="1025" y="240" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 主要连接箭头 -->
        <mxCell id="main_arrow1" value="" style="endArrow=classic;html=1;strokeColor=#4CAF50;strokeWidth=4;fillColor=#4CAF50;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="920" y="220" as="sourcePoint" />
            <mxPoint x="940" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="main_arrow2" value="" style="endArrow=classic;html=1;strokeColor=#4CAF50;strokeWidth=4;fillColor=#4CAF50;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1160" y="220" as="sourcePoint" />
            <mxPoint x="1200" y="220" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 输出层 -->
        <mxCell id="output_module" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F5E8;strokeColor=#4CAF50;strokeWidth=2;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="1220" y="120" width="240" height="200" as="geometry" />
        </mxCell>

        <mxCell id="output_title" value="定位预测输出层" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;fontColor=#4CAF50;" vertex="1" parent="1">
          <mxGeometry x="1290" y="90" width="140" height="25" as="geometry" />
        </mxCell>

        <!-- 最终特征表示 -->
        <mxCell id="final_features" value="最终节点特征&#xa;H_final ∈ ℝⁿˣᵈ_out" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#4CAF50;fontSize=12;fontColor=#4CAF50;align=center;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1240" y="140" width="120" height="50" as="geometry" />
        </mxCell>

        <!-- 位置预测 -->
        <mxCell id="position_prediction" value="位置预测&#xa;(x, y) ∈ ℝ²" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#4CAF50;fontSize=12;fontColor=#4CAF50;align=center;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1380" y="140" width="100" height="50" as="geometry" />
        </mxCell>

        <!-- ELU激活函数 -->
        <mxCell id="elu_activation" value="ELU激活函数&#xa;σ(x) = x if x≥0&#xa;α(e^x-1) if x&lt;0" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#C8E6C9;strokeColor=#4CAF50;fontSize=10;fontColor=#4CAF50;align=center;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1240" y="210" width="120" height="60" as="geometry" />
        </mxCell>

        <!-- 损失函数 -->
        <mxCell id="loss_function" value="MSE损失函数&#xa;L = ||ŷ - y||₂²" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#C8E6C9;strokeColor=#4CAF50;fontSize=10;fontColor=#4CAF50;align=center;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1380" y="210" width="100" height="60" as="geometry" />
        </mxCell>

        <!-- 输出连接箭头 -->
        <mxCell id="output_arrow1" value="" style="endArrow=classic;html=1;strokeColor=#4CAF50;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1360" y="165" as="sourcePoint" />
            <mxPoint x="1380" y="165" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 异构图结构详细展示 -->
        <mxCell id="hetero_graph_detail" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FAFAFA;strokeColor=#757575;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="40" y="400" width="600" height="220" as="geometry" />
        </mxCell>

        <mxCell id="hetero_graph_title" value="异构图结构与关系类型" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;fontColor=#424242;" vertex="1" parent="1">
          <mxGeometry x="260" y="370" width="200" height="25" as="geometry" />
        </mxCell>

        <!-- 节点类型展示 -->
        <mxCell id="node_types_section" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#757575;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="60" y="420" width="260" height="180" as="geometry" />
        </mxCell>

        <mxCell id="node_types_title" value="节点类型" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#424242;" vertex="1" parent="1">
          <mxGeometry x="160" y="430" width="80" height="20" as="geometry" />
        </mxCell>

        <!-- 标签节点 -->
        <mxCell id="tag_nodes_group" value="标签节点 (Tag)" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#E91E63;" vertex="1" parent="1">
          <mxGeometry x="80" y="460" width="100" height="20" as="geometry" />
        </mxCell>

        <mxCell id="tag_node1" value="T₁" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FCE4EC;strokeColor=#E91E63;fontSize=10;fontColor=#E91E63;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="480" width="25" height="25" as="geometry" />
        </mxCell>
        <mxCell id="tag_node2" value="T₂" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FCE4EC;strokeColor=#E91E63;fontSize=10;fontColor=#E91E63;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="115" y="480" width="25" height="25" as="geometry" />
        </mxCell>
        <mxCell id="tag_node3" value="T₃" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FCE4EC;strokeColor=#E91E63;fontSize=10;fontColor=#E91E63;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="150" y="480" width="25" height="25" as="geometry" />
        </mxCell>
        <mxCell id="tag_dots" value="⋯" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#E91E63;" vertex="1" parent="1">
          <mxGeometry x="185" y="485" width="15" height="15" as="geometry" />
        </mxCell>

        <!-- 天线节点 -->
        <mxCell id="antenna_nodes_group" value="天线节点 (Antenna)" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#1976D2;" vertex="1" parent="1">
          <mxGeometry x="80" y="520" width="120" height="20" as="geometry" />
        </mxCell>

        <mxCell id="antenna_node1" value="A₁" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;fontSize=10;fontColor=#1976D2;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="540" width="25" height="25" as="geometry" />
        </mxCell>
        <mxCell id="antenna_node2" value="A₂" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;fontSize=10;fontColor=#1976D2;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="115" y="540" width="25" height="25" as="geometry" />
        </mxCell>
        <mxCell id="antenna_node3" value="A₃" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;fontSize=10;fontColor=#1976D2;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="150" y="540" width="25" height="25" as="geometry" />
        </mxCell>
        <mxCell id="antenna_node4" value="A₄" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;fontSize=10;fontColor=#1976D2;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="185" y="540" width="25" height="25" as="geometry" />
        </mxCell>

        <!-- 椅子节点 -->
        <mxCell id="chair_nodes_group" value="椅子节点 (Chair)" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#795548;" vertex="1" parent="1">
          <mxGeometry x="80" y="570" width="100" height="20" as="geometry" />
        </mxCell>

        <mxCell id="chair_node1" value="C₁" style="ellipse;whiteSpace=wrap;html=1;fillColor=#EFEBE9;strokeColor=#795548;fontSize=10;fontColor=#795548;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="590" width="25" height="25" as="geometry" />
        </mxCell>
        <mxCell id="chair_node2" value="C₂" style="ellipse;whiteSpace=wrap;html=1;fillColor=#EFEBE9;strokeColor=#795548;fontSize=10;fontColor=#795548;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="115" y="590" width="25" height="25" as="geometry" />
        </mxCell>

        <!-- 关系类型展示 -->
        <mxCell id="edge_types_section" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#757575;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="340" y="420" width="280" height="180" as="geometry" />
        </mxCell>

        <mxCell id="edge_types_title" value="异构图关系类型" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#424242;" vertex="1" parent="1">
          <mxGeometry x="440" y="430" width="120" height="20" as="geometry" />
        </mxCell>

        <!-- T-T关系 -->
        <mxCell id="tt_relation" value="T-T: 标签间关系" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#E91E63;" vertex="1" parent="1">
          <mxGeometry x="360" y="460" width="100" height="15" as="geometry" />
        </mxCell>
        <mxCell id="tt_edge" value="" style="endArrow=none;html=1;strokeColor=#E91E63;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="470" y="467" as="sourcePoint" />
            <mxPoint x="500" y="467" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="tt_formula" value="w_ij^{T-T} = 1/(1+d_rssi²(v_i,v_j))" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontStyle=2;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="510" y="460" width="100" height="15" as="geometry" />
        </mxCell>

        <!-- T-A关系 -->
        <mxCell id="ta_relation" value="T-A: 标签-天线关系" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#FF9800;" vertex="1" parent="1">
          <mxGeometry x="360" y="485" width="110" height="15" as="geometry" />
        </mxCell>
        <mxCell id="ta_edge" value="" style="endArrow=none;html=1;strokeColor=#FF9800;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="470" y="492" as="sourcePoint" />
            <mxPoint x="500" y="492" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ta_formula" value="基于RSSI信号强度" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontStyle=2;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="510" y="485" width="100" height="15" as="geometry" />
        </mxCell>

        <!-- A-T关系 -->
        <mxCell id="at_relation" value="A-T: 天线-标签关系" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#1976D2;" vertex="1" parent="1">
          <mxGeometry x="360" y="510" width="110" height="15" as="geometry" />
        </mxCell>
        <mxCell id="at_edge" value="" style="endArrow=classic;html=1;strokeColor=#1976D2;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="470" y="517" as="sourcePoint" />
            <mxPoint x="500" y="517" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="at_formula" value="w_ij^{T-A} = 1/(1+d_spatial²(v_i,v_j))" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontStyle=2;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="510" y="510" width="100" height="15" as="geometry" />
        </mxCell>

        <!-- C-T关系 -->
        <mxCell id="ct_relation" value="C-T: 椅子遮挡关系" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#795548;" vertex="1" parent="1">
          <mxGeometry x="360" y="535" width="110" height="15" as="geometry" />
        </mxCell>
        <mxCell id="ct_edge" value="" style="endArrow=none;html=1;strokeColor=#795548;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="470" y="542" as="sourcePoint" />
            <mxPoint x="500" y="542" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="ct_formula" value="信号衰减建模" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontStyle=2;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="510" y="535" width="100" height="15" as="geometry" />
        </mxCell>

        <!-- 注意力权重公式 -->
        <mxCell id="attention_formula_detail" value="注意力权重计算：&#xa;α_ij^(r) = exp(LeakyReLU(a_r^T·W_r[h_i∥h_j∥e_ij])) / Σ_k exp(...)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=2;fontColor=#424242;" vertex="1" parent="1">
          <mxGeometry x="360" y="565" width="240" height="30" as="geometry" />
        </mxCell>

        <!-- 算法复杂度与性能分析 -->
        <mxCell id="complexity_analysis" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F3E5F5;strokeColor=#7B1FA2;strokeWidth=2;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="680" y="400" width="300" height="220" as="geometry" />
        </mxCell>

        <mxCell id="complexity_title" value="算法复杂度与性能" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#7B1FA2;" vertex="1" parent="1">
          <mxGeometry x="780" y="370" width="140" height="25" as="geometry" />
        </mxCell>

        <!-- 时间复杂度 -->
        <mxCell id="time_complexity" value="时间复杂度" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#7B1FA2;" vertex="1" parent="1">
          <mxGeometry x="700" y="420" width="100" height="20" as="geometry" />
        </mxCell>

        <mxCell id="complexity_formula" value="O(|E| · d_hidden · H)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#7B1FA2;fontSize=12;fontColor=#7B1FA2;align=center;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="700" y="445" width="120" height="30" as="geometry" />
        </mxCell>

        <mxCell id="complexity_explanation" value="其中：|E|为边数&#xa;d_hidden为隐藏维度&#xa;H为注意力头数" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="830" y="445" width="120" height="30" as="geometry" />
        </mxCell>

        <!-- 空间复杂度 -->
        <mxCell id="space_complexity" value="空间复杂度" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#7B1FA2;" vertex="1" parent="1">
          <mxGeometry x="700" y="490" width="100" height="20" as="geometry" />
        </mxCell>

        <mxCell id="space_formula" value="O(|V| · d + |E|)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#7B1FA2;fontSize=12;fontColor=#7B1FA2;align=center;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="700" y="515" width="120" height="30" as="geometry" />
        </mxCell>

        <mxCell id="space_explanation" value="其中：|V|为节点数&#xa;d为特征维度" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="830" y="515" width="120" height="30" as="geometry" />
        </mxCell>

        <!-- 性能优化 -->
        <mxCell id="performance_optimization" value="性能优化技术" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#7B1FA2;" vertex="1" parent="1">
          <mxGeometry x="700" y="560" width="120" height="20" as="geometry" />
        </mxCell>

        <mxCell id="optimization_list" value="• 动态K值邻居选择&#xa;• 边权重自适应调整&#xa;• 残差连接防止梯度消失&#xa;• 多头注意力并行计算" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="700" y="580" width="240" height="30" as="geometry" />
        </mxCell>

        <!-- 主图标题 -->
        <mxCell id="figure_caption" value="图 1. 基于GATv2的RFID异构图注意力特征学习网络架构" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;fontColor=#1976D2;" vertex="1" parent="1">
          <mxGeometry x="400" y="650" width="600" height="30" as="geometry" />
        </mxCell>

        <!-- 技术说明 -->
        <mxCell id="technical_note" value="该架构采用三层GATv2网络，通过异构图建模RFID系统中标签、天线和障碍物的复杂关系，&#xa;实现高精度的室内定位。多头注意力机制和残差连接确保了模型的表达能力和训练稳定性。" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=2;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="200" y="680" width="800" height="40" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
