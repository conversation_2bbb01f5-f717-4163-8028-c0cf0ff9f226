<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" etag="xxx" version="22.1.16" type="device">
  <diagram name="图注意力特征学习" id="graph-attention-learning">
    <mxGraphModel dx="1600" dy="900" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1400" pageHeight="700" math="1" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- 输入特征模块 -->
        <mxCell id="input_module" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F4FD;strokeColor=#1F497D;strokeWidth=2;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="150" width="180" height="100" as="geometry" />
        </mxCell>

        <mxCell id="input_title" value="RSSI信号特征" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#1F497D;" vertex="1" parent="1">
          <mxGeometry x="140" y="120" width="100" height="25" as="geometry" />
        </mxCell>

        <!-- 特征矩阵 -->
        <mxCell id="feature_matrix" value="h₁&#xa;h₂&#xa;⋮&#xa;hₙ" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#1F497D;fontSize=12;fontColor=#1F497D;align=center;" vertex="1" parent="1">
          <mxGeometry x="100" y="170" width="40" height="60" as="geometry" />
        </mxCell>

        <mxCell id="edge_features" value="e₁₂&#xa;e₁₃&#xa;⋮&#xa;eᵢⱼ" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F0F0F0;strokeColor=#666666;fontSize=12;fontColor=#666666;align=center;" vertex="1" parent="1">
          <mxGeometry x="150" y="170" width="40" height="60" as="geometry" />
        </mxCell>

        <mxCell id="input_notation" value="X ∈ ℝⁿˣᵈ" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=2;fontColor=#1F497D;" vertex="1" parent="1">
          <mxGeometry x="200" y="190" width="50" height="20" as="geometry" />
        </mxCell>

        <!-- 箭头1 -->
        <mxCell id="arrow1" value="" style="endArrow=classic;html=1;strokeColor=#2C3E50;strokeWidth=3;fillColor=#2C3E50;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="280" y="200" as="sourcePoint" />
            <mxPoint x="340" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- GATv2注意力机制模块 -->
        <mxCell id="attention_module" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF2CC;strokeColor=#D6B656;strokeWidth=2;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="360" y="120" width="280" height="160" as="geometry" />
        </mxCell>

        <mxCell id="attention_title" value="GATv2注意力机制" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="450" y="90" width="120" height="25" as="geometry" />
        </mxCell>

        <!-- 步骤1：特征拼接 -->
        <mxCell id="step1_box" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#D6B656;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="380" y="140" width="240" height="30" as="geometry" />
        </mxCell>

        <mxCell id="step1_label" value="1. 特征拼接" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="390" y="145" width="70" height="20" as="geometry" />
        </mxCell>

        <mxCell id="step1_formula" value="[hᵢ ∥ hⱼ ∥ eᵢⱼ]" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=2;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="480" y="145" width="120" height="20" as="geometry" />
        </mxCell>

        <!-- 步骤2：线性变换 -->
        <mxCell id="step2_box" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#D6B656;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="380" y="180" width="240" height="30" as="geometry" />
        </mxCell>

        <mxCell id="step2_label" value="2. 线性变换" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="390" y="185" width="70" height="20" as="geometry" />
        </mxCell>

        <mxCell id="step2_formula" value="Wᵣ · [hᵢ ∥ hⱼ ∥ eᵢⱼ]" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=2;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="480" y="185" width="130" height="20" as="geometry" />
        </mxCell>

        <!-- 步骤3：激活与归一化 -->
        <mxCell id="step3_box" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#D6B656;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="380" y="220" width="240" height="30" as="geometry" />
        </mxCell>

        <mxCell id="step3_label" value="3. LeakyReLU + Softmax" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="390" y="225" width="120" height="20" as="geometry" />
        </mxCell>

        <mxCell id="attention_weight_result" value="αᵢⱼ⁽ʳ⁾" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFD966;strokeColor=#D6B656;fontSize=12;fontStyle=1;fontColor=#B7950B;" vertex="1" parent="1">
          <mxGeometry x="560" y="220" width="50" height="30" as="geometry" />
        </mxCell>

        <!-- 箭头2 -->
        <mxCell id="arrow2" value="" style="endArrow=classic;html=1;strokeColor=#2C3E50;strokeWidth=3;fillColor=#2C3E50;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="660" y="200" as="sourcePoint" />
            <mxPoint x="720" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 多头注意力聚合模块 -->
        <mxCell id="multihead_module" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;strokeWidth=2;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="740" y="120" width="280" height="160" as="geometry" />
        </mxCell>

        <mxCell id="multihead_title" value="多头注意力聚合" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="830" y="90" width="120" height="25" as="geometry" />
        </mxCell>

        <!-- 多个注意力头 -->
        <mxCell id="head1" value="Head 1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#9673A6;fontSize=10;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="760" y="140" width="60" height="25" as="geometry" />
        </mxCell>

        <mxCell id="head2" value="Head 2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#9673A6;fontSize=10;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="830" y="140" width="60" height="25" as="geometry" />
        </mxCell>

        <mxCell id="head3" value="Head H" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#9673A6;fontSize=10;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="900" y="140" width="60" height="25" as="geometry" />
        </mxCell>

        <mxCell id="dots" value="⋯" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="970" y="145" width="20" height="15" as="geometry" />
        </mxCell>

        <!-- 拼接操作 -->
        <mxCell id="concat_operation" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#9673A6;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="760" y="180" width="230" height="40" as="geometry" />
        </mxCell>

        <mxCell id="concat_label" value="拼接操作：" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="770" y="185" width="60" height="15" as="geometry" />
        </mxCell>

        <mxCell id="concat_formula" value="Concat(h₁⁽ˡ⁺¹'¹⁾, h₁⁽ˡ⁺¹'²⁾, ..., h₁⁽ˡ⁺¹'ᴴ⁾)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontStyle=2;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="770" y="200" width="210" height="15" as="geometry" />
        </mxCell>

        <!-- 残差连接 -->
        <mxCell id="residual_connection" value="+ 残差连接" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F0F0F0;strokeColor=#9673A6;fontSize=10;fontColor=#6C2C91;" vertex="1" parent="1">
          <mxGeometry x="760" y="240" width="80" height="25" as="geometry" />
        </mxCell>

        <!-- 箭头3 -->
        <mxCell id="arrow3" value="" style="endArrow=classic;html=1;strokeColor=#2C3E50;strokeWidth=3;fillColor=#2C3E50;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="1040" y="200" as="sourcePoint" />
            <mxPoint x="1100" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 输出模块 -->
        <mxCell id="output_module" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;strokeWidth=2;shadow=1;" vertex="1" parent="1">
          <mxGeometry x="1120" y="150" width="180" height="100" as="geometry" />
        </mxCell>

        <mxCell id="output_title" value="更新后的节点特征" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1170" y="120" width="120" height="25" as="geometry" />
        </mxCell>

        <!-- 更新公式 -->
        <mxCell id="update_formula" value="h₍ᵢ₎⁽ˡ⁺¹⁾ = σ(∑ᵣ ∑ⱼ αᵢⱼ⁽ʳ⁾ Wᵣ⁽ˡ⁾ hⱼ⁽ˡ⁾)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=2;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1130" y="180" width="160" height="40" as="geometry" />
        </mxCell>

        <!-- ELU激活函数 -->
        <mxCell id="elu_activation" value="σ = ELU" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#82B366;fontSize=10;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="1130" y="220" width="60" height="20" as="geometry" />
        </mxCell>

        <!-- 异构图示例 -->
        <mxCell id="graph_example" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F8F9FA;strokeColor=#6C757D;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="320" width="300" height="120" as="geometry" />
        </mxCell>

        <mxCell id="graph_title" value="异构图结构示例" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#495057;" vertex="1" parent="1">
          <mxGeometry x="180" y="290" width="100" height="25" as="geometry" />
        </mxCell>

        <!-- 天线节点 -->
        <mxCell id="antenna_node1" value="A₁" style="ellipse;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;fontSize=10;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="120" y="340" width="25" height="25" as="geometry" />
        </mxCell>
        <mxCell id="antenna_node2" value="A₂" style="ellipse;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;fontSize=10;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="180" y="340" width="25" height="25" as="geometry" />
        </mxCell>
        <mxCell id="antenna_node3" value="A₃" style="ellipse;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;fontSize=10;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="240" y="340" width="25" height="25" as="geometry" />
        </mxCell>
        <mxCell id="antenna_node4" value="A₄" style="ellipse;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;fontSize=10;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="300" y="340" width="25" height="25" as="geometry" />
        </mxCell>

        <!-- 标签节点 -->
        <mxCell id="tag_node1" value="T₁" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;fontSize=10;fontColor=#C62828;" vertex="1" parent="1">
          <mxGeometry x="120" y="390" width="25" height="25" as="geometry" />
        </mxCell>
        <mxCell id="tag_node2" value="T₂" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;fontSize=10;fontColor=#C62828;" vertex="1" parent="1">
          <mxGeometry x="180" y="390" width="25" height="25" as="geometry" />
        </mxCell>
        <mxCell id="tag_node3" value="T₃" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;fontSize=10;fontColor=#C62828;" vertex="1" parent="1">
          <mxGeometry x="240" y="390" width="25" height="25" as="geometry" />
        </mxCell>
        <mxCell id="tag_node4" value="T₄" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;fontSize=10;fontColor=#C62828;" vertex="1" parent="1">
          <mxGeometry x="300" y="390" width="25" height="25" as="geometry" />
        </mxCell>

        <!-- 连接边 -->
        <mxCell id="edge1" value="" style="endArrow=none;html=1;strokeColor=#6C757D;strokeWidth=1;" edge="1" parent="1" source="antenna_node1" target="tag_node1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge2" value="" style="endArrow=none;html=1;strokeColor=#6C757D;strokeWidth=1;" edge="1" parent="1" source="antenna_node1" target="tag_node2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge3" value="" style="endArrow=none;html=1;strokeColor=#6C757D;strokeWidth=1;" edge="1" parent="1" source="antenna_node2" target="tag_node2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge4" value="" style="endArrow=none;html=1;strokeColor=#6C757D;strokeWidth=1;" edge="1" parent="1" source="antenna_node3" target="tag_node3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edge5" value="" style="endArrow=none;html=1;strokeColor=#6C757D;strokeWidth=1;" edge="1" parent="1" source="antenna_node4" target="tag_node4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="450" as="sourcePoint" />
            <mxPoint x="450" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 图例 -->
        <mxCell id="legend_box" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#6C757D;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="320" width="200" height="120" as="geometry" />
        </mxCell>

        <mxCell id="legend_title" value="图例" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#495057;" vertex="1" parent="1">
          <mxGeometry x="520" y="290" width="60" height="25" as="geometry" />
        </mxCell>

        <!-- 图例项目 -->
        <mxCell id="legend_antenna" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="470" y="340" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="legend_antenna_text" value="天线节点" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#495057;" vertex="1" parent="1">
          <mxGeometry x="500" y="340" width="60" height="20" as="geometry" />
        </mxCell>

        <mxCell id="legend_tag" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F8CECC;strokeColor=#B85450;" vertex="1" parent="1">
          <mxGeometry x="470" y="370" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="legend_tag_text" value="标签节点" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#495057;" vertex="1" parent="1">
          <mxGeometry x="500" y="370" width="60" height="20" as="geometry" />
        </mxCell>

        <mxCell id="legend_attention" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FFD966;strokeColor=#D6B656;" vertex="1" parent="1">
          <mxGeometry x="470" y="400" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="legend_attention_text" value="注意力权重" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#495057;" vertex="1" parent="1">
          <mxGeometry x="500" y="400" width="80" height="20" as="geometry" />
        </mxCell>

        <!-- 主图标题 -->
        <mxCell id="figure_caption" value="图 4. GATv2异构图注意力机制架构" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#D32F2F;" vertex="1" parent="1">
          <mxGeometry x="550" y="480" width="300" height="30" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
